#ifndef BLUETOOTH_HELPERS_H
#define BLUETOOTH_HELPERS_H

#include <Arduino.h>
#include "BluetoothA2DP.h"

#define BT_DISCOVER_TIME 10000 // 10 seconds for discovery

extern int deviceCount;         // To count how many unique devices we've discovered

// Function to connect to a TWS device
void connectToTWS(DeviceInfo device);

// Function to display all discovered devices
void showAllDevices();

// Callback for device discovery
void btDeviceFound(esp_bd_addr_t bda, esp_bt_dev_type_t dev_type, esp_bt_cod_t cod, const char* name);

// Function to start Bluetooth discovery
void startBluetoothDiscovery();

// Function to initialize Bluetooth
void initBluetooth();

#endif