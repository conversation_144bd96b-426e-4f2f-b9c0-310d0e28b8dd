#ifndef BLUETOOTH_A2DP_H
#define BLUETOOTH_A2DP_H

#include <Arduino.h>
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"
#include "esp_gap_bt_api.h"
#include "esp_a2dp_api.h"
#include "esp_avrc_api.h"

// The esp_bt_cod_t is already defined in esp_gap_bt_api.h

// Maximum number of devices to store
#define MAX_BT_DEVICES 5

// A2DP connection states
typedef enum {
    A2DP_DISCONNECTED,
    A2DP_CONNECTING,
    A2DP_CONNECTED,
    A2DP_DISCONNECTING
} a2dp_conn_state_t;

// Define a structure to store the MAC address and the device name
struct DeviceInfo {
  String macAddress;
  String name;
  uint8_t addr[ESP_BD_ADDR_LEN];
};

// Bluetooth device discovery callback
typedef void (*bt_device_found_cb)(esp_bd_addr_t bda, esp_bt_dev_type_t dev_type, esp_bt_cod_t cod, const char* name);

// Class for managing Bluetooth A2DP source functionality
class BluetoothA2DP {
public:
    BluetoothA2DP();

    // Initialize Bluetooth stack and A2DP source profile
    bool init(const char* device_name);

    // Start device discovery
    bool startDiscovery(uint32_t duration_ms = 10000);

    // Stop device discovery
    bool stopDiscovery();

    // Connect to a device by MAC address
    bool connect(const DeviceInfo& device);

    // Disconnect from current device
    bool disconnect();

    // Get current connection state
    a2dp_conn_state_t getConnectionState();

    // Check if currently connected
    bool isConnected();

    // Get the name of the connected device
    String getConnectedDeviceName();

    // Set callback for device discovery
    void setDeviceFoundCallback(bt_device_found_cb callback);

    // Get discovered devices
    DeviceInfo* getDevices(int* count);

    // AVRC control methods
    bool play();
    bool pause();
    bool stop();
    bool nextTrack();
    bool previousTrack();
    bool volumeUp();
    bool volumeDown();

private:
    // Bluetooth event handlers
    static void gap_callback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param);
    static void a2dp_callback(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param);
    static void avrc_callback(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param);

    // Current connection state
    a2dp_conn_state_t _conn_state;

    // Connected device address
    esp_bd_addr_t _connected_bda;

    // Connected device name
    String _connected_device_name;

    // Discovered devices
    DeviceInfo _devices[MAX_BT_DEVICES];
    int _device_count;
    int _device_index;

    // Device discovery callback
    bt_device_found_cb _device_found_cb;

    // Singleton instance for callbacks
    static BluetoothA2DP* _instance;
};

// Global instance
extern BluetoothA2DP btA2DP;

#endif // BLUETOOTH_A2DP_H
