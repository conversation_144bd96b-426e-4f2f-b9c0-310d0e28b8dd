#ifndef BLUETOOTH_UTILS_H
#define BLUETOOTH_UTILS_H

#include <Arduino.h>
#include "esp_gap_bt_api.h"

// Function to check if a device is a TWS/headphone
inline bool isTWSDevice(esp_bt_cod_t cod) {
    // Extract the major and minor device class from the COD
    uint32_t cod_value = cod.minor << 8 | cod.major << 12 | cod.service << 16;
    
    // Check if device is in the Audio/Video major class (0x04)
    if ((cod_value >> 8 & 0x1F) == 0x04) {
        // Check for headphones, speakers, or other audio devices
        uint32_t minor_class = (cod_value >> 2) & 0x3F;
        if ((minor_class & 0x18) || (minor_class & 0x14)) {
            return true;
        }
    }
    
    // If we can't determine for sure, assume it might be compatible
    return true;
}

#endif // BLUETOOTH_UTILS_H
