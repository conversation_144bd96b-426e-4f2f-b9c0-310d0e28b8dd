#include "BluetoothHelpers.h"
#include "BluetoothA2DP.h"
#include "BluetoothUtils.h"

DeviceInfo devices[5];   // Store the discovered devices
int newDeviceIndex = 0;  // To map new device
int deviceCount = 0;     // To count how many unique devices we've discovered

void connectToTWS(DeviceInfo device) {
  // Connect to the selected TWS device using A2DP
  btA2DP.connect(device);
}

void showAllDevices() {
  Serial.println("Device list:");
  for (int i = 0; i < deviceCount; i++) {
    Serial.print(i);
    Serial.print(": Device Name: ");
    Serial.print(devices[i].name);
    Serial.print(", MAC Address: ");
    Serial.println(devices[i].macAddress);
  }
}

// Callback for device discovery
void btDeviceFound(esp_bd_addr_t bda, esp_bt_dev_type_t dev_type, esp_bt_cod_t cod, const char* name) {
  // Format MAC address as string
  char mac_str[18];
  sprintf(mac_str, "%02x:%02x:%02x:%02x:%02x:%02x",
          bda[0], bda[1], bda[2], bda[3], bda[4], bda[5]);

  // Check if this is a TWS device (headphone/audio device)
  if (isTWSDevice(cod)) {
    Serial.print("Found TWS device: ");
    Serial.print(name);
    Serial.print(", MAC: ");
    Serial.println(mac_str);

    // Store device info
    devices[newDeviceIndex].macAddress = String(mac_str);
    devices[newDeviceIndex].name = String(name);
    memcpy(devices[newDeviceIndex].addr, bda, sizeof(esp_bd_addr_t));

    // Increment newDeviceIndex and handle wrapping around
    newDeviceIndex = (newDeviceIndex + 1) % 5;

    // Increment deviceCount only if it's less than 5
    if (deviceCount < 5) {
      deviceCount++;
    }
  }
}

// Function to start Bluetooth discovery
void startBluetoothDiscovery() {
  Serial.println("Starting Bluetooth discovery...");
  deviceCount = 0;  // Reset device count
  newDeviceIndex = 0;  // Reset device index
  btA2DP.startDiscovery(BT_DISCOVER_TIME);
}

// Function to initialize Bluetooth
void initBluetooth() {
  Serial.println("Initializing Bluetooth...");

  // Initialize A2DP with device name
  if (btA2DP.init("ESPOD")) {
    // Set device discovery callback
    btA2DP.setDeviceFoundCallback(btDeviceFound);

    // Start device discovery
    startBluetoothDiscovery();
  } else {
    Serial.println("Failed to initialize Bluetooth A2DP");
  }
}

// The isTWSDevice function is now defined in BluetoothA2DP.cpp
