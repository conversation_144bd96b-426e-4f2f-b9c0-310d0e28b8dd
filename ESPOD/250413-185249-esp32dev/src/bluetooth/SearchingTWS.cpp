#include "SearchingTWS.h"
#include "BluetoothA2DP.h"

int selectedBlDeviceIndex = -1;

void renderConnectingTWS() {
  if (selectedBlDeviceIndex < 0 || selectedBlDeviceIndex >= deviceCount) {
    Serial.println("Invalid device selection");
    currentState = SEARCHING_TWS;
    return;
  }

  // Get the list of discovered devices
  int count;
  DeviceInfo* devices = btA2DP.getDevices(&count);
  if (devices == NULL || count <= 0) {
    Serial.println("No devices available");
    currentState = SEARCHING_TWS;
    return;
  }

  DeviceInfo selectedDevice = devices[selectedBlDeviceIndex];

  // Only show connecting message if not already connected
  if (btA2DP.getConnectionState() != A2DP_CONNECTED) {
    Serial.print("Connecting to ");
    Serial.println(selectedDevice.name);

    // Connect to the selected TWS device
    connectToTWS(selectedDevice);
  }

  // Check connection state
  a2dp_conn_state_t state = btA2DP.getConnectionState();

  switch (state) {
    case A2DP_CONNECTED:
      Serial.println("Connected successfully!");
      currentState = CONNECTED;
      break;

    case A2DP_CONNECTING:
      Serial.println("Still connecting...");
      break;

    case A2DP_DISCONNECTED:
      Serial.println("Connection failed. Returning to search.");
      currentState = SEARCHING_TWS;
      break;

    default:
      break;
  }

  delay(1000);
}

void renderSearchingTWS() {
  Serial.print("Searching TWS devices... Selected: ");
  Serial.print(selectedBlDeviceIndex);
  Serial.print(", Found: ");
  Serial.println(deviceCount);

  // If no devices found yet, start discovery if not already running
  if (deviceCount == 0) {
    selectedBlDeviceIndex = -1;
    startBluetoothDiscovery();
  } else {
    // Show all discovered devices
    showAllDevices();

    // If no device is selected yet, select the first one
    if (selectedBlDeviceIndex < 0 && deviceCount > 0) {
      selectedBlDeviceIndex = 0;
    }
  }

  delay(2000);
}

void processBtnClickDuringSearchingTWS(char key) {
  if (deviceCount < 1) {
    selectedBlDeviceIndex = -1;
    return;
  }

  // Handle button presses
  switch (key) {
    case '1':
      // Refresh device list
      Serial.println("Refreshing device list...");
      startBluetoothDiscovery();
      break;

    case '2':
      // Previous device
      if (selectedBlDeviceIndex > 0) {
        selectedBlDeviceIndex--;
        Serial.print("Selected device: ");
        Serial.println(selectedBlDeviceIndex);
      }
      break;

    case '3':
      // Next device
      if ((selectedBlDeviceIndex + 1) < deviceCount) {
        selectedBlDeviceIndex++;
        Serial.print("Selected device: ");
        Serial.println(selectedBlDeviceIndex);
      }
      break;

    case '4':
      // Connect to selected device
      if (selectedBlDeviceIndex >= 0) {
        Serial.println("Initiating connection...");
        currentState = CONNECTING;
      }
      break;

    default:
      break;
  }
}
